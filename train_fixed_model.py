#!/usr/bin/env python3
"""
修复后的多模态绝缘子检测训练脚本
"""

import os
import sys
import yaml
import torch
import argparse
from datetime import datetime

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def main():
    parser = argparse.ArgumentParser(description='修复后的多模态训练')
    parser.add_argument('--config', type=str, default='configs/config_fixed_retrain.yaml', help='配置文件路径')
    parser.add_argument('--resume', type=str, help='恢复训练的权重路径')
    parser.add_argument('--device', type=str, default='auto', help='训练设备')
    
    args = parser.parse_args()
    
    print("🚀 [RETRAIN] 启动修复后的多模态绝缘子检测训练...")
    print(f"📁 配置文件: {args.config}")
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    print(f"🔧 训练设备: {device}")
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"runs/train_fixed_model/{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存配置副本
    config_copy_path = os.path.join(output_dir, 'config.yaml')
    with open(config_copy_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"📁 输出目录: {output_dir}")
    print("🔧 关键修复:")
    print("  ✅ 检测头权重正确初始化")
    print("  ✅ 置信度偏置设为-4.6")
    print("  ✅ 类别权重配置")
    print("  ✅ 优化损失权重")
    
    # 导入训练模块
    from src.training.train_multimodal import MultimodalTrainer

    # 转换配置格式以匹配训练器期望的格式
    training_config = {
        'data_dir': config.get('data_dir', 'data'),
        'output_dir': output_dir,
        'nc': config['model']['nc'],
        'img_size': config.get('img_size', 640),
        'batch_size': config.get('batch_size', 16),
        'epochs': config['training']['epochs'],
        'num_workers': config.get('num_workers', 0),
        'grad_clip': 10.0,

        'model': {
            'type': 'simple',  # 使用simple类型，内部会创建FixedMultimodalYOLO
            'yolo_model_path': 'yolov8n.pt',
            'fusion_type': config['model']['fusion_type']
        },

        'optimizer': {
            'type': 'Adam',
            'lr': config['training']['lr'],
            'weight_decay': config['training']['weight_decay']
        },

        'scheduler': {
            'type': 'CosineAnnealingLR',
            'T_max': config['training']['epochs']
        },

        'early_stopping': {
            'patience': config['training']['patience']
        }
    }

    # Windows系统下强制设置num_workers=0
    import platform
    if platform.system() == 'Windows':
        print("[INFO] Windows系统检测到，自动设置 num_workers=0")
        training_config['num_workers'] = 0

    print("\n📋 训练配置:")
    print(f"  数据目录: {training_config['data_dir']}")
    print(f"  输出目录: {training_config['output_dir']}")
    print(f"  类别数: {training_config['nc']}")
    print(f"  训练轮数: {training_config['epochs']}")
    print(f"  批次大小: {training_config['batch_size']}")
    print(f"  学习率: {training_config['optimizer']['lr']}")
    print(f"  图像尺寸: {training_config['img_size']}")

    # 开始训练
    try:
        trainer = MultimodalTrainer(training_config)
        if args.resume:
            print(f"📂 恢复训练: {args.resume}")
            # 这里可以添加恢复训练的逻辑
        trainer.train()
        print("🎉 训练完成！")
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
