import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import yaml
from tqdm import tqdm
import argparse
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

# 添加src目录到Python路径
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入自定义模块
from src.dataset.multimodal_dataset import create_dataloader
from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO, MultimodalLoss
print("使用修复版多模态YOLO模型")
from src.models.multimodal_fusion import EarlyFusionModule


class MultimodalTrainer:
    """多模态模型训练器"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 创建输出目录
        self.output_dir = config['output_dir']
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'weights'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'logs'), exist_ok=True)
        
        # 创建TensorBoard记录器
        self.writer = SummaryWriter(os.path.join(self.output_dir, 'logs'))
        
        # 初始化模型
        self.model = self._create_model()
        
        # 初始化损失函数
        self.criterion = MultimodalLoss(nc=config['nc'], device=self.device)
        
        # 初始化优化器
        self.optimizer = self._create_optimizer()
        
        # 初始化学习率调度器
        self.scheduler = self._create_scheduler()
        
        # 创建数据加载器
        self.train_loader = create_dataloader(
            data_dir=config['data_dir'],
            split='train',
            batch_size=config['batch_size'],
            img_size=config['img_size'],
            num_workers=config['num_workers'],
            shuffle=True
        )
        
        self.val_loader = create_dataloader(
            data_dir=config['data_dir'],
            split='valid',
            batch_size=config['batch_size'],
            img_size=config['img_size'],
            num_workers=config['num_workers'],
            shuffle=False
        )
        
        # 训练状态
        self.start_epoch = 0
        self.best_val_loss = float('inf')
        self.train_losses = []
        self.val_losses = []
        
        # 早停状态
        self.patience_counter = 0
        self.min_delta = config.get('early_stopping', {}).get('min_delta', 0.001)
        
        # 加载预训练权重或恢复训练
        if config.get('resume'):
            self._load_checkpoint(config['resume'])
        elif config.get('pretrained'):
            self._load_pretrained(config['pretrained'])
    
    def _create_model(self):
        """创建模型"""
        model_config = self.config['model']
        
        if model_config['type'] == 'simple':
            model = SimpleMultimodalYOLO(
                nc=self.config['nc'],
                fusion_type=model_config.get('fusion_type', 'cross_attention')
            )
        else:
            raise ValueError(f"不支持的模型类型: {model_config['type']}")
        
        model = model.to(self.device)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"模型总参数数: {total_params:,}")
        print(f"可训练参数数: {trainable_params:,}")
        
        return model
    
    def _create_optimizer(self):
        """创建优化器"""
        optimizer_config = self.config['optimizer']
        
        if optimizer_config['type'] == 'Adam':
            optimizer = optim.Adam(
                self.model.parameters(),
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config.get('weight_decay', 1e-4)
            )
        elif optimizer_config['type'] == 'SGD':
            optimizer = optim.SGD(
                self.model.parameters(),
                lr=optimizer_config['lr'],
                momentum=optimizer_config.get('momentum', 0.9),
                weight_decay=optimizer_config.get('weight_decay', 1e-4)
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_config['type']}")
        
        return optimizer
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler_config = self.config.get('scheduler', {})
        
        if scheduler_config.get('type') == 'StepLR':
            scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_config.get('type') == 'CosineAnnealingLR':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config['epochs']
            )
        else:
            scheduler = None
        
        return scheduler
    
    def _load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        if os.path.exists(checkpoint_path):
            print(f"加载检查点: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
            if self.scheduler and 'scheduler_state_dict' in checkpoint:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            self.start_epoch = checkpoint['epoch']
            self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
            self.train_losses = checkpoint.get('train_losses', [])
            self.val_losses = checkpoint.get('val_losses', [])
            # 重置早停计数器，恢复训练时重新开始计算
            self.patience_counter = 0
            
            print(f"从第 {self.start_epoch} 轮恢复训练")
        else:
            print(f"检查点文件不存在: {checkpoint_path}")
    
    def _load_pretrained(self, pretrained_path):
        """加载预训练权重"""
        if os.path.exists(pretrained_path):
            print(f"加载预训练权重: {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location=self.device)
            
            if 'model_state_dict' in checkpoint:
                model_dict = checkpoint['model_state_dict']
            else:
                model_dict = checkpoint
            
            # 过滤不匹配的权重
            pretrained_dict = {}
            model_dict_keys = self.model.state_dict().keys()
            
            for k, v in model_dict.items():
                if k in model_dict_keys and self.model.state_dict()[k].shape == v.shape:
                    pretrained_dict[k] = v
                else:
                    print(f"跳过不匹配的权重: {k}")
            
            # 加载权重
            self.model.load_state_dict(pretrained_dict, strict=False)
            print(f"成功加载 {len(pretrained_dict)} 个权重参数")
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = len(self.train_loader)
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.config["epochs"]}')
        
        for batch_idx, batch in enumerate(pbar):
            rgb_images = batch['rgb'].to(self.device)
            thermal_images = batch['thermal'].to(self.device)
            targets = batch['targets'].to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            
            try:
                outputs = self.model(rgb_images, thermal_images)
                loss, loss_items = self.criterion(outputs, targets)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                if self.config.get('grad_clip'):
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.config['grad_clip']
                    )
                
                self.optimizer.step()
                
                # 记录损失
                total_loss += loss.item()
                
                # 更新进度条
                pbar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'box': f'{loss_items[0]:.4f}',
                    'obj': f'{loss_items[1]:.4f}',
                    'cls': f'{loss_items[2]:.4f}',
                    'lr': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
                })
                
                # 记录到TensorBoard
                global_step = epoch * num_batches + batch_idx
                self.writer.add_scalar('Train/Loss', loss.item(), global_step)
                self.writer.add_scalar('Train/BoxLoss', loss_items[0].item(), global_step)
                self.writer.add_scalar('Train/ObjLoss', loss_items[1].item(), global_step)
                self.writer.add_scalar('Train/ClsLoss', loss_items[2].item(), global_step)
                self.writer.add_scalar('Train/LR', self.optimizer.param_groups[0]['lr'], global_step)
                
            except Exception as e:
                print(f"训练批次错误: {e}")
                continue
        
        avg_loss = total_loss / num_batches
        self.train_losses.append(avg_loss)
        
        return avg_loss
    
    def validate_epoch(self, epoch):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                rgb_images = batch['rgb'].to(self.device)
                thermal_images = batch['thermal'].to(self.device)
                targets = batch['targets'].to(self.device)
                
                try:
                    outputs = self.model(rgb_images, thermal_images)
                    loss, loss_items = self.criterion(outputs, targets)
                    total_loss += loss.item()
                    
                except Exception as e:
                    print(f"验证批次错误: {e}")
                    continue
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0
        self.val_losses.append(avg_loss)
        
        # 记录到TensorBoard
        self.writer.add_scalar('Val/Loss', avg_loss, epoch)
        
        return avg_loss
    
    def save_checkpoint(self, epoch, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch + 1,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_loss': self.best_val_loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'config': self.config
        }
        
        if self.scheduler:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # 保存最新的检查点
        checkpoint_path = os.path.join(self.output_dir, 'weights', 'last.pt')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.output_dir, 'weights', 'best.pt')
            torch.save(checkpoint, best_path)
            print(f"保存最佳模型: {best_path}")
        
        # 定期保存检查点
        if (epoch + 1) % 10 == 0:
            epoch_path = os.path.join(self.output_dir, 'weights', f'epoch_{epoch+1}.pt')
            torch.save(checkpoint, epoch_path)
    
    def train(self):
        """完整训练流程"""
        print("开始训练...")
        print(f"训练数据: {len(self.train_loader)} 批次")
        print(f"验证数据: {len(self.val_loader)} 批次")
        
        for epoch in range(self.start_epoch, self.config['epochs']):
            # 训练
            train_loss = self.train_epoch(epoch)
            
            # 验证
            val_loss = self.validate_epoch(epoch)
            
            # 更新学习率
            if self.scheduler:
                self.scheduler.step()
            
            # 打印日志
            print(f'Epoch {epoch+1}/{self.config["epochs"]}:')
            print(f'  Train Loss: {train_loss:.4f}')
            print(f'  Val Loss: {val_loss:.4f}')
            print(f'  LR: {self.optimizer.param_groups[0]["lr"]:.6f}')
            
            # 早停检查 - 使用标准的递减容忍度逻辑
            if self.config.get('early_stopping'):
                patience = self.config['early_stopping']['patience']
                
                # 检查是否有显著改善（考虑最小改善阈值）
                if val_loss < self.best_val_loss - self.min_delta:
                    self.patience_counter = 0
                    improvement = self.best_val_loss - val_loss
                    print(f"验证损失显著改善: {val_loss:.4f} (改善幅度: {improvement:.4f})")
                else:
                    self.patience_counter += 1
                    print(f"验证损失无显著改善 ({self.patience_counter}/{patience})")
                
                # 如果连续patience轮没有改善，则早停
                if self.patience_counter >= patience:
                    print(f"早停：验证损失连续 {patience} 轮无显著改善 (min_delta={self.min_delta})")
                    break
            
            # 保存模型（在早停检查后，避免逻辑冲突）
            is_best = val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = val_loss
            
            self.save_checkpoint(epoch, is_best)
        
        # 保存训练历史
        history = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss
        }
        
        with open(os.path.join(self.output_dir, 'training_history.json'), 'w') as f:
            json.dump(history, f, indent=2)
        
        self.writer.close()
        print("训练完成!")


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_default_config():
    """创建默认配置"""
    config = {
        'data_dir': 'data',
        'output_dir': 'runs/train',
        'nc': 5,  # 修复：使用正确的类别数5
        'img_size': 640,
        'batch_size': 16,
        'epochs': 100,
        'num_workers': 4,
        'grad_clip': 10.0,
        
        'model': {
            'type': 'simple',
            'yolo_model_path': 'yolov8n.pt',
            'fusion_type': 'cross_attention'
        },
        
        'optimizer': {
            'type': 'Adam',
            'lr': 0.001,
            'weight_decay': 1e-4
        },
        
        'scheduler': {
            'type': 'CosineAnnealingLR'
        },
        
        'early_stopping': {
            'patience': 10
        }
    }
    return config


def main():
    parser = argparse.ArgumentParser(description='多模态绝缘子检测模型训练')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--data_dir', type=str, help='数据集目录')
    parser.add_argument('--output_dir', type=str, help='输出目录')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    parser.add_argument('--lr', type=float, help='学习率')
    parser.add_argument('--resume', type=str, help='恢复训练的检查点路径')
    parser.add_argument('--pretrained', type=str, help='预训练权重路径')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config:
        config = load_config(args.config)
        print(f"[INFO] 已加载配置文件: {args.config}")
    else:
        config = create_default_config()
        print("[INFO] 使用默认配置")
    
    # 命令行参数覆盖配置（仅当显式提供时）
    if args.data_dir is not None:
        config['data_dir'] = args.data_dir
        print(f"[OVERRIDE] data_dir: {args.data_dir}")
    if args.output_dir is not None:
        config['output_dir'] = args.output_dir
        print(f"[OVERRIDE] output_dir: {args.output_dir}")
    if args.epochs is not None:
        config['epochs'] = args.epochs
        print(f"[OVERRIDE] epochs: {args.epochs}")
    if args.batch_size is not None:
        config['batch_size'] = args.batch_size
        print(f"[OVERRIDE] batch_size: {args.batch_size}")
    if args.lr is not None:
        config['optimizer']['lr'] = args.lr
        print(f"[OVERRIDE] learning_rate: {args.lr}")
    if args.resume is not None:
        config['resume'] = args.resume
        print(f"[OVERRIDE] resume: {args.resume}")
    if args.pretrained is not None:
        config['pretrained'] = args.pretrained
        print(f"[OVERRIDE] pretrained: {args.pretrained}")
    
    # Windows系统下强制设置num_workers=0以避免多进程问题
    import platform
    if platform.system() == 'Windows':
        print("[INFO] Windows系统检测到，自动设置 num_workers=0")
        config['num_workers'] = 0
    
    # 添加时间戳到输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    config['output_dir'] = os.path.join(config['output_dir'], timestamp)
    
    # 保存配置
    os.makedirs(config['output_dir'], exist_ok=True)
    with open(os.path.join(config['output_dir'], 'config.yaml'), 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print("训练配置:")
    print(yaml.dump(config, default_flow_style=False, allow_unicode=True))
    
    # 开始训练
    trainer = MultimodalTrainer(config)
    trainer.train()


if __name__ == '__main__':
    # Windows多进程支持
    import multiprocessing
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn')
        except RuntimeError:
            pass  # 可能已经设置过了
    
    main() 